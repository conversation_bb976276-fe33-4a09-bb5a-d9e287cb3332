using System.Collections.Concurrent;
using System.IO.Ports;
using System.Net;
using Gsdt.ModbusGateway.Services;
using Microsoft.Extensions.Logging;
using NetCoreServer;
using NModbus;

namespace Gsdt.ModbusGateway.Models;

/// <summary>
/// Tcp服务
/// </summary>
public class ModbusTcpServer : TcpServer
{
    private readonly ILogger<ModbusTcpGatewayService> _logger;
    private readonly IModbusRequestHandler _modbusRequestHandler;
    private readonly ThreadSafeModbusClientPool _clientPool;
    private readonly GatewayConfig _config;
    private readonly CancellationTokenSource _stoppingCts;

    public ModbusTcpServer(IPAddress address, int port,
        IModbusRequestHandler modbusRequestHandler, ILogger<ModbusTcpGatewayService> logger,
        GatewayConfig config,
        ThreadSafeModbusClientPool clientPool,
        CancellationTokenSource stoppingCts) : base(address, port)
    {
        _logger = logger;
        _config = config;
        _modbusRequestHandler = modbusRequestHandler;
        _clientPool = clientPool;
        _stoppingCts = stoppingCts;
    }

    protected override TcpSession CreateSession() =>
        new ModbusTcpSession(this, _logger, _modbusRequestHandler, _clientPool, _config, _stoppingCts);
}

public class ModbusTcpSession : TcpSession
{
    private readonly ILogger<ModbusTcpGatewayService> _logger;
    private readonly IModbusRequestHandler _modbusRequestHandler;
    private readonly ConcurrentDictionary<int, ModbusClient> _modbusClients;
    private readonly GatewayConfig _config;
    private readonly CancellationTokenSource _stoppingCts;

    public ModbusTcpSession(
        TcpServer server,
        ILogger<ModbusTcpGatewayService> logger,
        IModbusRequestHandler modbusRequestHandler,
        ConcurrentDictionary<int, ModbusClient> modbusClients,
        GatewayConfig config,
        CancellationTokenSource stoppingCts) : base(server)
    {
        _logger = logger;
        _modbusRequestHandler = modbusRequestHandler;
        _modbusClients = modbusClients;
        _config = config;
        _stoppingCts = stoppingCts;
    }

    protected override void OnReceived(byte[] buffer, long offset, long size)
    {
        // 创建请求的副本
        var request = new byte[size];
        Array.Copy(buffer, request, size);

        // 异步处理请求但不等待
        _ = ProcessRequestAsync(request).ContinueWith(t =>
        {
            if (t.IsFaulted)
            {
                _logger.LogError(t.Exception, $"处理来自 {Server.Address}:{Server.Port} 的请求时发生未处理的异常");
            }
        }, TaskScheduler.Current);
    }

    private async Task ProcessRequestAsync(byte[] request)
    {
        // 获取从站ID（第7个字节，因为Modbus TCP有6字节的MBAP头）
        var slaveId = request[6];

        // 获取功能码（第8个字节）
        var functionCode = request[7];

        // 获取起始地址（第9和10个字节）
        var startAddress = (ushort)((request[8] << 8) | request[9]);

        // 获取数量（第11和12个字节）
        var quantity = (ushort)((request[10] << 8) | request[11]);

        _logger.LogInformation(
            "收到请求：Port={Port}, FunctionCode={FunctionCode}, StartAddress={startAddress}, Quantity={quantity}",
            Server.Port, functionCode, startAddress, quantity);

        try
        {
            // 查找对应的路由配置
            var port = Server.Port;
            var routeConfig = _config.Routes.FirstOrDefault(r => r.Port == port);
            if (routeConfig == null)
            {
                _logger.LogWarning("未找到端口为 {Port} 的路由配置", port);
                throw new InvalidModbusRequestException($"未找到端口为 {port} 的路由配置",
                    (byte)ModbusExceptionCode.GatewayPathUnavailable);
            }

            // 根据路由转换成请求列表
            var requests = TransformRequest(request, routeConfig);
            if (requests.Count == 0)
            {
                _logger.LogWarning("未找到起始地址为 {StartAddress} 的转换配置", startAddress);
                throw new InvalidModbusRequestException($"未找到起始地址为 {startAddress} 的转换配置",
                    (byte)ModbusExceptionCode.GatewayPathUnavailable);
            }

            var responseList = new List<ModbusResponse>();
            foreach (var modbusRequest in requests)
            {
                _logger.LogInformation(
                    $"转发请求到: DeviceId={modbusRequest.DeviceId}, FunctionCode={functionCode}, SlaveId={modbusRequest.SlaveId}, StartAddress={modbusRequest.StartAddress}, Quantity={modbusRequest.Quantity}");
                var targetClient = GetModbusClient(modbusRequest);
                // 修改请求报文中的从机地址、起始地址和寄存器数量
                var newRequest = ModifyRequest(request, modbusRequest.SlaveId, modbusRequest.StartAddress,
                    modbusRequest.Quantity);
                var data = await _modbusRequestHandler.ForwardRequestAsync(targetClient, newRequest, slaveId,
                    _stoppingCts.Token);
                responseList.Add(new ModbusResponse
                {
                    Data = data,
                    SourceStartAddress = modbusRequest.SourceStartAddress
                });
            }

            // 合并响应报文
            var response = await _modbusRequestHandler.MergeResponsesAsync(request, responseList);
            SendAsync(response);
        }
        catch (InvalidModbusRequestException ex)
        {
            SendExceptionResponse(request, slaveId, ex.ExceptionCode);
        }
        catch (SlaveException ex)
        {
            _logger.LogError(ex, "目标设备返回错误: {Message},功能码={FunctionCode}, 错误码={ExceptionCode}", ex.Message,
                ex.FunctionCode, ex.SlaveExceptionCode);
            SendExceptionResponse(request, slaveId, (byte)ModbusExceptionCode.GatewayTargetDeviceFailedToRespond);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "处理Modbus请求时发生错误");
            SendExceptionResponse(request, slaveId, (byte)ModbusExceptionCode.GatewayTargetDeviceFailedToRespond);
        }
    }

    private void SendExceptionResponse(byte[] request, byte originalSlaveId, byte exceptionCode)
    {
        var response = ModbusRequestHandler.CreateExceptionResponse(request, originalSlaveId, exceptionCode);
        SendAsync(response);
    }

    /// <summary>
    /// 修改Modbus TCP请求报文的起始地址和寄存器数量
    /// </summary>
    /// <param name="originalRequest">原始请求字节数组</param>
    /// <param name="newSlaveId">新的从机地址</param>
    /// <param name="newStartAddress">新的起始地址</param>
    /// <param name="newQuantity">新的寄存器数量</param>
    /// <returns>修改后的请求字节数组</returns>
    private static byte[] ModifyRequest(byte[] originalRequest, ushort newSlaveId, ushort newStartAddress,
        ushort newQuantity)
    {
        // 复制原始请求以避免修改原数组
        var modifiedRequest = (byte[])originalRequest.Clone();

        // Modbus TCP请求中各字段的偏移位置
        const int slaveIdOffset = 6; // 从机地址（Unit Identifier）
        const int functionCodeOffset = 7; // 功能码
        const int startAddressOffset = 8; // 起始地址
        const int quantityOffset = 10; // 寄存器数量

        // 验证请求长度是否足够
        if (modifiedRequest.Length < 12)
        {
            throw new ArgumentException("Invalid Modbus TCP request length");
        }

        // 修改从机地址（1字节）
        modifiedRequest[slaveIdOffset] = (byte)newSlaveId;

        // 修改起始地址（大端序）
        modifiedRequest[startAddressOffset] = (byte)(newStartAddress >> 8);
        modifiedRequest[startAddressOffset + 1] = (byte)(newStartAddress & 0xFF);

        if (modifiedRequest[functionCodeOffset] < 5)
        {
            // 修改寄存器数量（大端序）
            modifiedRequest[quantityOffset] = (byte)(newQuantity >> 8);
            modifiedRequest[quantityOffset + 1] = (byte)(newQuantity & 0xFF);
        }

        return modifiedRequest;
    }

    /// <summary>
    /// 根据原始请求和路由配置转换成请求列表
    /// </summary>
    /// <param name="request">原始Modbus TCP请求</param>
    /// <param name="route">路由配置</param>
    /// <returns>转换后的请求列表</returns>
    private static List<ModbusRequest> TransformRequest(
        byte[] request,
        RouteConfig route)
    {
        // 从请求中提取必要信息
        var originalSlaveId = request[6];
        var functionCode = request[7];
        var originalStart = (ushort)((request[8] << 8) | request[9]);
        var originalQuantity = (ushort)((request[10] << 8) | request[11]);

        var requests = new List<ModbusRequest>();

        // 如果是写入寄存器，则直接转到目标地址；
        if (functionCode >= 5)
        {
            var transform = route.Transforms.FirstOrDefault(t => t.FunctionCodes.Contains(functionCode) && t.SourceStartAddress == originalStart);
            requests.Add(transform == null
                ? CreateDefaultRequest(route.DefaultDeviceId, originalSlaveId, originalStart, originalQuantity)
                : CreateDefaultRequest(transform.TargetDeviceId, transform.TargetSlaveId, transform.TargetStartAddress, originalQuantity));
            return requests;
        }

        // 根据路由配置转换请求
        var originalEnd = (ushort)(originalStart + originalQuantity - 1);
        var currentPosition = originalStart;
        var remaining = originalQuantity;
        var transforms = route.Transforms.OrderBy(t => t.SourceStartAddress).ToList();

        foreach (var transform in transforms)
        {
            if (!transform.FunctionCodes.Contains(functionCode))
            {
                continue;
            }

            if (remaining <= 0) break;

            var transformStart = transform.SourceStartAddress;
            var transformEnd = (ushort)(transform.SourceStartAddress + transform.Quantity - 1);
            var overlapStart = Math.Max(currentPosition, transformStart);
            var overlapEnd = Math.Min(originalEnd, transformEnd);

            // 处理前置未覆盖部分
            if (currentPosition < overlapStart)
            {
                var defaultQuantity = Math.Min((ushort)(overlapStart - currentPosition), originalQuantity);
                requests.Add(CreateDefaultRequest(
                    route.DefaultDeviceId,
                    originalSlaveId,
                    currentPosition,
                    defaultQuantity));

                currentPosition += defaultQuantity;
                remaining -= defaultQuantity;
            }

            // 处理覆盖部分
            if (overlapStart <= overlapEnd && remaining > 0)
            {
                var transformQuantity = (ushort)(overlapEnd - overlapStart + 1);
                requests.Add(CreateTransformRequest(
                    transform,
                    overlapStart,
                    transformQuantity));

                currentPosition += transformQuantity;
                remaining -= transformQuantity;
            }
        }

        // 处理剩余部分
        if (remaining > 0)
        {
            requests.Add(CreateDefaultRequest(
                route.DefaultDeviceId,
                originalSlaveId,
                currentPosition,
                remaining));
        }

        return requests;
    }

    private static ModbusRequest CreateDefaultRequest(int deviceId, ushort slaveId, ushort start, ushort quantity)
    {
        return new ModbusRequest
        {
            DeviceId = deviceId,
            SlaveId = slaveId,
            StartAddress = start,
            Quantity = quantity,
            SourceStartAddress = start
        };
    }

    private static ModbusRequest CreateTransformRequest(TransformRout transform, ushort overlapStart, ushort quantity)
    {
        return new ModbusRequest
        {
            DeviceId = transform.TargetDeviceId,
            SlaveId = transform.TargetSlaveId,
            StartAddress = (ushort)(transform.TargetStartAddress + (overlapStart - transform.SourceStartAddress)),
            Quantity = quantity,
            SourceStartAddress = transform.SourceStartAddress
        };
    }

    private ModbusClient GetModbusClient(ModbusRequest modbusRequest)
    {
        // 查找对应的目标设备
        var targetDevice = _config.TargetDevices.FirstOrDefault(d => d.Id == modbusRequest.DeviceId);
        if (targetDevice == null)
        {
            _logger.LogWarning("未找到ID为 {DeviceId} 的目标设备", modbusRequest.DeviceId);
            throw new InvalidModbusRequestException($"未找到ID为 {modbusRequest.DeviceId} 的目标设备",
                (byte)ModbusExceptionCode.GatewayPathUnavailable);
        }

        ModbusClient? targetClient;

        // 根据目标设备的端口类型，选择不同的Modbus客户端
        if (targetDevice.PortType == PortType.Tcp)
        {
            if (targetDevice is not TcpTargetDevice tcpTargetDevice)
            {
                _logger.LogWarning("目标设备配置错误, DeviceId= {DeviceId}", targetDevice.Id);
                throw new InvalidModbusRequestException($"目标设备配置错误, DeviceId= {targetDevice.Id}",
                    (byte)ModbusExceptionCode.GatewayPathUnavailable);
            }

            // 查找对应的Modbus客户端
            if (!_modbusClients.TryGetValue(targetDevice.Id, out targetClient) ||
                targetClient is not ModbusTcpClient targetTcpClient)
            {
                _logger.LogWarning("未找到ID为 {DeviceId} 的Modbus客户端", targetDevice.Id);
                throw new InvalidModbusRequestException($"未找到ID为 {targetDevice.Id} 的Modbus客户端",
                    (byte)ModbusExceptionCode.GatewayPathUnavailable);
            }

            // 确保客户端已连接
            if (!targetTcpClient.IsConnected)
            {
                _logger.LogInformation("重新连接到设备 {DeviceId} ({IpAddress}:{Port})",
                    tcpTargetDevice.Id, tcpTargetDevice.Ip, tcpTargetDevice.Port);

                try
                {
                    targetTcpClient.Connect(new IPEndPoint(IPAddress.Parse(tcpTargetDevice.Ip),
                        tcpTargetDevice.Port));
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "重新连接到设备 {DeviceId} 失败", tcpTargetDevice.Id);
                    throw new InvalidModbusRequestException($"重新连接到设备 {tcpTargetDevice.Id} 失败",
                        (byte)ModbusExceptionCode.GatewayTargetDeviceFailedToRespond);
                }
            }
        }
        else
        {
            if (targetDevice is not RtuTargetDevice rtuTargetDevice)
            {
                _logger.LogWarning("目标设备配置错误, DeviceId= {DeviceId}", targetDevice.Id);
                throw new InvalidModbusRequestException($"目标设备配置错误, DeviceId= {targetDevice.Id}",
                    (byte)ModbusExceptionCode.GatewayPathUnavailable);
            }

            // 查找对应的Modbus客户端
            if (!_modbusClients.TryGetValue(targetDevice.Id, out targetClient) ||
                targetClient is not ModbusRtuClient targetRtuClient)
            {
                _logger.LogWarning("未找到ID为 {DeviceId} 的Modbus客户端", targetDevice.Id);
                throw new InvalidModbusRequestException($"未找到ID为 {targetDevice.Id} 的Modbus客户端",
                    (byte)ModbusExceptionCode.GatewayPathUnavailable);
            }

            // 确保客户端已连接
            if (!targetRtuClient.IsConnected)
            {
                _logger.LogInformation("重新连接到设备 {DeviceId}, PortName = {Port})", rtuTargetDevice.Id,
                    rtuTargetDevice.PortName);

                try
                {
                    targetRtuClient.Connect(new SerialPort(rtuTargetDevice.PortName, rtuTargetDevice.BaudRate,
                        rtuTargetDevice.Parity, rtuTargetDevice.DataBits, rtuTargetDevice.StopBits));
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "重新连接到设备 {DeviceId} 失败", rtuTargetDevice.Id);
                    throw new InvalidModbusRequestException($"重新连接到设备 {rtuTargetDevice.Id} 失败",
                        (byte)ModbusExceptionCode.GatewayTargetDeviceFailedToRespond);
                }
            }
        }

        return targetClient;
    }
}