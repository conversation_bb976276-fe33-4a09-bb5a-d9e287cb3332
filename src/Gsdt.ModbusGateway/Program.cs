using System.Runtime.InteropServices;
using System.Text.Json;
using Gsdt.ModbusGateway;
using Gsdt.ModbusGateway.Models;
using Gsdt.ModbusGateway.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Serilog;
using Serilog.Settings.Configuration;

if (!RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
{
    Console.WriteLine("警告：此应用程序主要在Windows平台上测试。");
}

// 加载配置文件
var configuration = new ConfigurationBuilder()
    .SetBasePath(AppContext.BaseDirectory) // 控制台需显式设置路径[5](@ref)
    .AddJsonFile("appsettings.json")
    .Build();

var options = new ConfigurationReaderOptions(
    typeof(ConsoleLoggerConfigurationExtensions).Assembly,
    typeof(FileLoggerConfigurationExtensions).Assembly);

// 配置 Serilog（AOT 兼容方式）
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(configuration, options)
    .CreateLogger();

// // 配置Serilog
// Log.Logger = new LoggerConfiguration()
//     .MinimumLevel.Debug()
//     .MinimumLevel.Override("Microsoft", LogEventLevel.Information)
//     .Enrich.FromLogContext()
//     .WriteTo.Console()
//     .WriteTo.File(
//         Path.Combine("logs", "log-.txt"),
//         rollingInterval: RollingInterval.Day,
//         retainedFileCountLimit: 15,
//         outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}")
//     .CreateLogger();

var builder = Host.CreateDefaultBuilder(args)
    .ConfigureAppConfiguration((hostContext, config) => { config.AddConfiguration(configuration); })
    .UseSerilog()
    .ConfigureServices((hostContext, services) =>
    {
        // 配置 JSON 序列化选项
        services.Configure<JsonSerializerOptions>(options =>
        {
            options.TypeInfoResolver = AppJsonContext.Default;
            options.PropertyNameCaseInsensitive = true;
            options.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
            options.WriteIndented = true;
        });

        var gatewayConfig = new GatewayConfig();
        var gatewayConfigSection = hostContext.Configuration.GetSection("GatewayConfig");
        if (gatewayConfigSection == null)
        {
            throw new Exception("未找到GatewayConfig配置节");
        }

        gatewayConfig.TcpTargetDevices = gatewayConfigSection.GetSection("TcpTargetDevices").Get<List<TcpTargetDevice>>() ?? [];
        gatewayConfig.RtuTargetDevices = gatewayConfigSection.GetSection("RtuTargetDevices").Get<List<RtuTargetDevice>>() ?? [];
        gatewayConfig.Routes = gatewayConfigSection.GetSection("Routes").Get<List<RouteConfig>>() ?? [];

        services.AddSingleton(Options.Create(gatewayConfig));

        // 注册配置验证服务
        services.AddSingleton<IConfigValidationService, ConfigValidationService>();

        // 注册核心服务
        services.AddSingleton<IModbusClientFactory, ModbusClientFactory>();
        services.AddSingleton<IModbusRequestHandler, ModbusRequestHandler>();

        // 注册监控和健康检查服务
        services.AddSingleton<ModbusPerformanceMonitor>();
        services.AddSingleton<ModbusConnectionHealthService>();

        // 注册线程安全组件（依赖于性能监控服务）
        services.AddSingleton<ThreadSafeModbusClientPool>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<ThreadSafeModbusClientPool>>();
            var performanceMonitor = provider.GetRequiredService<ModbusPerformanceMonitor>();
            return new ThreadSafeModbusClientPool(logger, performanceMonitor);
        });

        // 注册后台服务
        services.AddHostedService<ModbusTcpGatewayService>();
        services.AddHostedService<ModbusConnectionHealthService>();
    });

var host = builder.Build();

// 在启动前验证配置
ValidateConfiguration(host.Services);

await host.RunAsync();
return;

// 配置验证方法
static void ValidateConfiguration(IServiceProvider services)
{
    var logger = services.GetRequiredService<ILogger<Program>>();
    var configValidationService = services.GetRequiredService<IConfigValidationService>();
    var config = services.GetRequiredService<IOptions<GatewayConfig>>().Value;

    try
    {
        logger.LogInformation("正在验证配置...");
        configValidationService.ValidateConfig(config);
        logger.LogInformation("配置验证通过");
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "配置验证失败");
        throw; // 重新抛出异常以终止应用程序
    }
}