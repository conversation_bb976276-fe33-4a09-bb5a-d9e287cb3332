using System.Text.Json.Serialization;
using Gsdt.ModbusGateway.Controllers;
using Gsdt.ModbusGateway.Models;
using Gsdt.ModbusGateway.Services;

namespace Gsdt.ModbusGateway;

[JsonSourceGenerationOptions(
    PropertyNamingPolicy = JsonKnownNamingPolicy.CamelCase,
    WriteIndented = true)]
[JsonSerializable(typeof(GatewayConfig))]
[JsonSerializable(typeof(TcpTargetDevice))]
[JsonSerializable(typeof(List<TcpTargetDevice>))]
[JsonSerializable(typeof(RtuTargetDevice))]
[JsonSerializable(typeof(List<RtuTargetDevice>))]
[JsonSerializable(typeof(RouteConfig))]
[JsonSerializable(typeof(List<RouteConfig>))]
[JsonSerializable(typeof(TransformRout))]
[JsonSerializable(typeof(List<TransformRout>))]
[JsonSerializable(typeof(SystemOverview))]
[JsonSerializable(typeof(DevicePerformanceSnapshot))]
[JsonSerializable(typeof(DeviceHealthStatus))]
[JsonSerializable(typeof(Dictionary<int, DevicePerformanceSnapshot>))]
[JsonSerializable(typeof(Dictionary<int, DeviceHealthStatus>))]
public partial class AppJsonContext : JsonSerializerContext;