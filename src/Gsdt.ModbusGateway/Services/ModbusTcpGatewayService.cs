using System.Collections.Concurrent;
using System.Net;
using Gsdt.ModbusGateway.Models;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Gsdt.ModbusGateway.Services;

/// <summary>
/// Modbus TCP 网关服务，使用 NModbus 库实现
/// 作为 Modbus TCP 服务器监听外部连接，并将请求转发到目标设备
/// </summary>
public class ModbusTcpGatewayService : IHostedService, IDisposable
{
    private readonly ILogger<ModbusTcpGatewayService> _logger;
    private readonly GatewayConfig _config;
    private readonly IModbusClientFactory _clientFactory;
    private readonly IModbusRequestHandler _modbusRequestHandler;
    private readonly ThreadSafeModbusClientPool _clientPool;
    private readonly CancellationTokenSource _stoppingCts = new();
    private ModbusTcpServer? _modbusTcpServer;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="config">网关配置</param>
    /// <param name="clientFactory">Modbus客户端工厂</param>
    /// <param name="modbusRequestHandler">modbus消息处理</param>
    public ModbusTcpGatewayService(
        ILogger<ModbusTcpGatewayService> logger,
        IOptions<GatewayConfig> config,
        IModbusClientFactory clientFactory,
        IModbusRequestHandler modbusRequestHandler)
    {
        _logger = logger;
        _config = config.Value;
        _clientFactory = clientFactory;
        _modbusRequestHandler = modbusRequestHandler;
    }

    /// <summary>
    /// 启动服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>异步任务</returns>
    public Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("正在启动Modbus TCP网关服务...");

        try
        {
            // 初始化所有目标设备的Modbus客户端
            foreach (var device in _config.TargetDevices)
            {
                try
                {
                    switch (device.PortType)
                    {
                        case PortType.Tcp:
                            var tcpClient = _clientFactory.CreateTcpClient(device);
                            _modbusClients[device.Id] = tcpClient;
                            break;
                        case PortType.Rtu:
                            var rtuClient = _clientFactory.CreateRtuClient(device);
                            _modbusClients[device.Id] = rtuClient;
                            break;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "初始化设备 {DeviceId} 的Modbus客户端时发生错误", device.Id);
                }
            }

            // 启动TCP监听器
            foreach (var listenPort in _config.ListenPorts)
            {
                _modbusTcpServer = new ModbusTcpServer(IPAddress.Any, listenPort, _modbusRequestHandler,_logger, _config, _stoppingCts);
                _modbusTcpServer.SetTargetModbusClients(_modbusClients);
                _modbusTcpServer.Start();

                _logger.LogInformation("Modbus TCP网关已启动，监听端口: {Port}", listenPort);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "启动Modbus TCP网关服务时发生错误");
            throw;
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// 停止服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>异步任务</returns>
    public async Task StopAsync(CancellationToken cancellationToken)
    {
        if (_modbusTcpServer == null)
        {
            return;
        }

        try
        {
            _logger.LogInformation("正在停止Modbus TCP网关服务...");

            await _stoppingCts.CancelAsync();

            // 停止TCP监听器
            _modbusTcpServer.Stop();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "停止Modbus TCP网关服务时发生错误");
        }

        _logger.LogInformation("Modbus TCP网关服务已停止");
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        _stoppingCts.Cancel();

        // 释放所有客户端资源
        foreach (var client in _modbusClients.Values)
        {
            try
            {
                if (client is ModbusTcpClient { IsConnected: true } tcpClient)
                {
                    tcpClient.Disconnect();
                    tcpClient.Dispose();
                    return;
                }

                if (client is ModbusRtuClient { IsConnected: true } rtuClient)
                {
                    rtuClient.Disconnect();
                    rtuClient.Dispose();
                    return;
                }

                client.Dispose();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "断开Modbus客户端连接时发生错误");
            }
        }

        _stoppingCts.Dispose();
        GC.SuppressFinalize(this);
    }
}
